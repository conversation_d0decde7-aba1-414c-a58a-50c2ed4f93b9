document.addEventListener('DOMContentLoaded', function() {
    const navButtons = document.querySelectorAll('.nav-btn');
    const stageContents = document.querySelectorAll('.stage-content');
    const cards = document.querySelectorAll('.card');
    
    // 导航切换功能
    navButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetStage = this.getAttribute('data-stage');
            
            // 移除所有活动状态
            navButtons.forEach(btn => btn.classList.remove('active'));
            stageContents.forEach(content => content.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(`stage-${targetStage}`).classList.add('active');
            
            // 添加切换动画
            const activeContent = document.getElementById(`stage-${targetStage}`);
            activeContent.style.opacity = '0';
            activeContent.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                activeContent.style.opacity = '1';
                activeContent.style.transform = 'translateY(0)';
            }, 50);
        });
    });
    
    // 卡片悬停效果增强
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // 滑动手势支持（移动端）
    let startX = 0;
    let startY = 0;
    let currentStageIndex = 0;
    const stages = ['0-3', '3-6', '6-12', '12-24'];
    
    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;
        
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        
        const diffX = startX - endX;
        const diffY = startY - endY;
        
        // 确保是水平滑动
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            if (diffX > 0 && currentStageIndex < stages.length - 1) {
                // 向左滑动，下一个阶段
                currentStageIndex++;
            } else if (diffX < 0 && currentStageIndex > 0) {
                // 向右滑动，上一个阶段
                currentStageIndex--;
            }
            
            // 触发对应按钮点击
            const targetButton = document.querySelector(`[data-stage="${stages[currentStageIndex]}"]`);
            if (targetButton) {
                targetButton.click();
            }
        }
        
        startX = 0;
        startY = 0;
    });
    
    // 获取当前激活的阶段索引
    function getCurrentStageIndex() {
        const activeButton = document.querySelector('.nav-btn.active');
        if (activeButton) {
            const stage = activeButton.getAttribute('data-stage');
            return stages.indexOf(stage);
        }
        return 0;
    }
    
    // 更新当前阶段索引
    navButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
            currentStageIndex = index;
        });
    });
    
    // 添加键盘导航支持
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft' && currentStageIndex > 0) {
            currentStageIndex--;
            const targetButton = document.querySelector(`[data-stage="${stages[currentStageIndex]}"]`);
            targetButton.click();
        } else if (e.key === 'ArrowRight' && currentStageIndex < stages.length - 1) {
            currentStageIndex++;
            const targetButton = document.querySelector(`[data-stage="${stages[currentStageIndex]}"]`);
            targetButton.click();
        }
    });
    
    // 添加滚动时的视差效果
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const header = document.querySelector('.header');
        
        if (header) {
            header.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
    
    // 添加卡片进入动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // 初始化卡片动画
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(card);
    });
    
    // 添加提示标签点击效果
    const tips = document.querySelectorAll('.tip');
    tips.forEach(tip => {
        tip.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            this.style.background = 'rgba(255,255,255,0.4)';
            
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                this.style.background = 'rgba(255,255,255,0.2)';
            }, 150);
        });
    });
    
    // 初始化当前阶段索引
    currentStageIndex = getCurrentStageIndex();
    
    // 添加页面加载完成后的欢迎动画
    setTimeout(() => {
        const header = document.querySelector('.header');
        header.style.animation = 'pulse 2s ease-in-out';
    }, 500);
});

// 添加脉冲动画
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
