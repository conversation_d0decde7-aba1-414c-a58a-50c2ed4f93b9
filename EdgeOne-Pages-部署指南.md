# EdgeOne Pages H5应用部署指南

## 📦 部署包准备完成

已为您准备好部署文件：
- **部署包**: `h5-subsidy-calculator-deploy.zip`
- **项目目录**: `h5-subsidy-calculator/`

## 🚀 部署方法

### 方法一：控制台上传部署（推荐）

1. **登录腾讯云控制台**
   - 访问: https://console.cloud.tencent.com/edgeone
   - 登录您的腾讯云账号

2. **进入EdgeOne Pages**
   - 在左侧菜单选择 "Pages"
   - 点击 "新建站点"

3. **配置站点**
   - 站点名称: `h5-subsidy-calculator`
   - 部署方式: 选择 "上传文件"
   - 上传 `h5-subsidy-calculator-deploy.zip`

4. **部署设置**
   ```
   构建命令: 无需构建
   输出目录: ./
   安装命令: 无需安装
   Node.js版本: 18.x
   ```

### 方法二：Git仓库部署

1. **推送代码到Git仓库**
   ```bash
   cd h5-subsidy-calculator
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin <your-repo-url>
   git push -u origin main
   ```

2. **在EdgeOne Pages中连接仓库**
   - 选择 "Git仓库" 部署方式
   - 连接您的GitHub/GitLab仓库
   - 选择 `h5-subsidy-calculator` 目录

### 方法三：CLI部署（如果有CLI工具）

```bash
# 安装EdgeOne CLI
npm install -g @tencent-cloud/edgeone-cli

# 登录
edgeone login

# 部署
cd h5-subsidy-calculator
edgeone deploy
```

## ⚙️ 重要配置

### 1. 域名配置
- 使用提供的默认域名或配置自定义域名
- 确保启用HTTPS（PWA功能需要）

### 2. 缓存策略
```
HTML文件: no-cache
CSS/JS文件: max-age=31536000
Service Worker: no-cache
```

### 3. 安全头配置
已在 `.edgeonerc` 中预配置：
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block

## 📋 部署检查清单

部署完成后，请验证：

- [ ] 页面正常加载
- [ ] 样式正确显示
- [ ] JavaScript功能正常
- [ ] 计算器功能可用
- [ ] PWA安装提示出现
- [ ] Service Worker注册成功
- [ ] 移动端适配良好
- [ ] HTTPS正常工作

## 🔧 故障排除

### 常见问题：

1. **页面空白**
   - 检查文件路径是否正确
   - 确认所有文件都已上传

2. **样式丢失**
   - 检查CSS文件路径
   - 确认MIME类型设置

3. **JavaScript错误**
   - 查看浏览器控制台
   - 检查文件完整性

4. **PWA功能异常**
   - 确认HTTPS环境
   - 检查manifest.json和sw.js

## 📱 访问测试

部署成功后，您可以：
1. 在桌面浏览器中访问
2. 在移动设备上测试
3. 尝试添加到主屏幕（PWA功能）
4. 测试离线功能

## 🎯 下一步

1. 配置自定义域名
2. 设置CDN加速
3. 配置监控和分析
4. 定期更新内容

---

**注意**: 如果您需要MCP工具支持，可以考虑：
1. 安装腾讯云CLI工具
2. 使用第三方部署工具
3. 编写自定义部署脚本
