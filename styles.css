* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.header {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    padding: 2rem 1rem;
    text-align: center;
    color: white;
}

.header h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1rem;
    opacity: 0.9;
}

.stage-nav {
    display: flex;
    background: #f8f9fa;
    padding: 0;
    overflow-x: auto;
    border-bottom: 2px solid #e9ecef;
}

.nav-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 80px;
}

.nav-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.nav-btn.active {
    background: #007bff;
    color: white;
    border-bottom: 3px solid #0056b3;
}

.content {
    padding: 1rem;
}

.stage-content {
    display: none;
    animation: fadeIn 0.5s ease-in;
}

.stage-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.stage-header h2 {
    font-size: 1.4rem;
    color: #2c3e50;
}

.age-badge {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.knowledge-cards {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.card ul {
    list-style: none;
}

.card li {
    padding: 0.3rem 0;
    position: relative;
    padding-left: 1.2rem;
    color: #555;
}

.card li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.tips-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 1.5rem;
    color: white;
}

.tips-section h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.tips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tip {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 1.5rem;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    .header {
        padding: 1.5rem 1rem;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .stage-nav {
        position: sticky;
        top: 0;
        z-index: 100;
    }
    
    .nav-btn {
        font-size: 0.8rem;
        padding: 0.8rem 0.5rem;
    }
    
    .stage-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .stage-header h2 {
        font-size: 1.2rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .tips {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .content {
        padding: 0.5rem;
    }
    
    .card {
        margin: 0.5rem 0;
    }
    
    .tips-section {
        margin: 0.5rem 0;
    }
}
