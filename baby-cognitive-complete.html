<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>婴儿认知发展小知识</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            padding: 2rem 1rem;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .stage-nav {
            display: flex;
            background: #f8f9fa;
            padding: 0;
            overflow-x: auto;
            border-bottom: 2px solid #e9ecef;
        }

        .nav-btn {
            flex: 1;
            padding: 1rem;
            border: none;
            background: transparent;
            font-size: 0.9rem;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 80px;
        }

        .nav-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .nav-btn.active {
            background: #007bff;
            color: white;
            border-bottom: 3px solid #0056b3;
        }

        .content {
            padding: 1rem;
        }

        .stage-content {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .stage-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stage-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f8f9fa;
        }

        .stage-header h2 {
            font-size: 1.4rem;
            color: #2c3e50;
        }

        .age-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .knowledge-cards {
            display: grid;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .card ul {
            list-style: none;
        }

        .card li {
            padding: 0.3rem 0;
            position: relative;
            padding-left: 1.2rem;
            color: #555;
        }

        .card li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .tips-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
        }

        .tips-section h4 {
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .tips {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tip {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            
            .header {
                padding: 1.5rem 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .stage-nav {
                position: sticky;
                top: 0;
                z-index: 100;
            }
            
            .nav-btn {
                font-size: 0.8rem;
                padding: 0.8rem 0.5rem;
            }
            
            .stage-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .stage-header h2 {
                font-size: 1.2rem;
            }
            
            .card {
                padding: 1rem;
            }
            
            .tips {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .content {
                padding: 0.5rem;
            }
            
            .card {
                margin: 0.5rem 0;
            }
            
            .tips-section {
                margin: 0.5rem 0;
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>👶 婴儿认知发展小知识</h1>
            <p class="subtitle">了解宝宝每个阶段的认知发展特点</p>
        </header>

        <nav class="stage-nav">
            <button class="nav-btn active" data-stage="0-3">0-3个月</button>
            <button class="nav-btn" data-stage="3-6">3-6个月</button>
            <button class="nav-btn" data-stage="6-12">6-12个月</button>
            <button class="nav-btn" data-stage="12-24">12-24个月</button>
        </nav>

        <main class="content">
            <!-- 0-3个月阶段 -->
            <section class="stage-content active" id="stage-0-3">
                <div class="stage-header">
                    <h2>🌟 0-3个月：感知觉发展期</h2>
                    <div class="age-badge">新生儿期</div>
                </div>
                
                <div class="knowledge-cards">
                    <div class="card">
                        <div class="card-icon">👁️</div>
                        <h3>视觉发展</h3>
                        <ul>
                            <li>出生时只能看到20-30cm距离的物体</li>
                            <li>喜欢黑白对比强烈的图案</li>
                            <li>2-3个月开始追视移动物体</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <div class="card-icon">👂</div>
                        <h3>听觉发展</h3>
                        <ul>
                            <li>对熟悉的声音有反应</li>
                            <li>喜欢妈妈的声音和心跳声</li>
                            <li>能区分不同音调和节奏</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <div class="card-icon">🤲</div>
                        <h3>触觉发展</h3>
                        <ul>
                            <li>通过触摸感知世界</li>
                            <li>喜欢柔软温暖的触感</li>
                            <li>握持反射逐渐发展</li>
                        </ul>
                    </div>
                </div>
                
                <div class="tips-section">
                    <h4>💡 家长指导建议</h4>
                    <div class="tips">
                        <span class="tip">多与宝宝进行眼神交流</span>
                        <span class="tip">播放轻柔的音乐</span>
                        <span class="tip">提供黑白对比的玩具</span>
                        <span class="tip">温柔的抚触按摩</span>
                    </div>
                </div>
            </section>

            <!-- 3-6个月阶段 -->
            <section class="stage-content" id="stage-3-6">
                <div class="stage-header">
                    <h2>🎯 3-6个月：主动探索期</h2>
                    <div class="age-badge">婴儿期</div>
                </div>

                <div class="knowledge-cards">
                    <div class="card">
                        <div class="card-icon">🎨</div>
                        <h3>视觉认知</h3>
                        <ul>
                            <li>开始识别颜色，特别是红色</li>
                            <li>能够追踪快速移动的物体</li>
                            <li>开始有深度知觉</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-icon">✋</div>
                        <h3>手眼协调</h3>
                        <ul>
                            <li>开始有意识地抓握物品</li>
                            <li>能将物品从一只手传到另一只手</li>
                            <li>喜欢把东西放进嘴里探索</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-icon">😊</div>
                        <h3>社交认知</h3>
                        <ul>
                            <li>开始社交性微笑</li>
                            <li>能识别熟悉的面孔</li>
                            <li>对陌生人表现出好奇</li>
                        </ul>
                    </div>
                </div>

                <div class="tips-section">
                    <h4>💡 家长指导建议</h4>
                    <div class="tips">
                        <span class="tip">提供彩色安全玩具</span>
                        <span class="tip">练习抓握游戏</span>
                        <span class="tip">多做面部表情互动</span>
                        <span class="tip">唱儿歌做手势</span>
                    </div>
                </div>
            </section>

            <!-- 6-12个月阶段 -->
            <section class="stage-content" id="stage-6-12">
                <div class="stage-header">
                    <h2>🚀 6-12个月：快速发展期</h2>
                    <div class="age-badge">婴儿期</div>
                </div>

                <div class="knowledge-cards">
                    <div class="card">
                        <div class="card-icon">🧩</div>
                        <h3>认知能力</h3>
                        <ul>
                            <li>理解物体恒存性概念</li>
                            <li>开始模仿简单动作</li>
                            <li>能够解决简单问题</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-icon">🗣️</div>
                        <h3>语言发展</h3>
                        <ul>
                            <li>开始发出"爸爸""妈妈"音节</li>
                            <li>理解简单指令</li>
                            <li>用手势表达需求</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-icon">🎪</div>
                        <h3>运动认知</h3>
                        <ul>
                            <li>坐立时能保持平衡</li>
                            <li>开始爬行探索环境</li>
                            <li>精细动作更加协调</li>
                        </ul>
                    </div>
                </div>

                <div class="tips-section">
                    <h4>💡 家长指导建议</h4>
                    <div class="tips">
                        <span class="tip">玩躲猫猫游戏</span>
                        <span class="tip">重复说简单词汇</span>
                        <span class="tip">提供爬行空间</span>
                        <span class="tip">读图画书</span>
                    </div>
                </div>
            </section>

            <!-- 12-24个月阶段 -->
            <section class="stage-content" id="stage-12-24">
                <div class="stage-header">
                    <h2>🌈 12-24个月：语言爆发期</h2>
                    <div class="age-badge">幼儿期</div>
                </div>

                <div class="knowledge-cards">
                    <div class="card">
                        <div class="card-icon">💭</div>
                        <h3>思维发展</h3>
                        <ul>
                            <li>开始使用符号思维</li>
                            <li>理解因果关系</li>
                            <li>记忆能力显著提升</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-icon">📚</div>
                        <h3>语言能力</h3>
                        <ul>
                            <li>词汇量快速增长</li>
                            <li>开始组合词语</li>
                            <li>理解复杂指令</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-icon">🎭</div>
                        <h3>社会认知</h3>
                        <ul>
                            <li>开始模仿游戏</li>
                            <li>表现出同理心</li>
                            <li>理解简单规则</li>
                        </ul>
                    </div>
                </div>

                <div class="tips-section">
                    <h4>💡 家长指导建议</h4>
                    <div class="tips">
                        <span class="tip">多与宝宝对话</span>
                        <span class="tip">提供角色扮演玩具</span>
                        <span class="tip">建立日常规律</span>
                        <span class="tip">鼓励探索新事物</span>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>💝 每个宝宝都是独特的，发展节奏也不同，请耐心陪伴成长</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const navButtons = document.querySelectorAll('.nav-btn');
            const stageContents = document.querySelectorAll('.stage-content');
            const cards = document.querySelectorAll('.card');

            // 导航切换功能
            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetStage = this.getAttribute('data-stage');

                    // 移除所有活动状态
                    navButtons.forEach(btn => btn.classList.remove('active'));
                    stageContents.forEach(content => content.classList.remove('active'));

                    // 添加当前活动状态
                    this.classList.add('active');
                    document.getElementById(`stage-${targetStage}`).classList.add('active');

                    // 添加切换动画
                    const activeContent = document.getElementById(`stage-${targetStage}`);
                    activeContent.style.opacity = '0';
                    activeContent.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        activeContent.style.opacity = '1';
                        activeContent.style.transform = 'translateY(0)';
                    }, 50);
                });
            });

            // 卡片悬停效果增强
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 滑动手势支持（移动端）
            let startX = 0;
            let startY = 0;
            let currentStageIndex = 0;
            const stages = ['0-3', '3-6', '6-12', '12-24'];

            document.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            document.addEventListener('touchend', function(e) {
                if (!startX || !startY) return;

                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;

                const diffX = startX - endX;
                const diffY = startY - endY;

                // 确保是水平滑动
                if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                    if (diffX > 0 && currentStageIndex < stages.length - 1) {
                        // 向左滑动，下一个阶段
                        currentStageIndex++;
                    } else if (diffX < 0 && currentStageIndex > 0) {
                        // 向右滑动，上一个阶段
                        currentStageIndex--;
                    }

                    // 触发对应按钮点击
                    const targetButton = document.querySelector(`[data-stage="${stages[currentStageIndex]}"]`);
                    if (targetButton) {
                        targetButton.click();
                    }
                }

                startX = 0;
                startY = 0;
            });

            // 获取当前激活的阶段索引
            function getCurrentStageIndex() {
                const activeButton = document.querySelector('.nav-btn.active');
                if (activeButton) {
                    const stage = activeButton.getAttribute('data-stage');
                    return stages.indexOf(stage);
                }
                return 0;
            }

            // 更新当前阶段索引
            navButtons.forEach((button, index) => {
                button.addEventListener('click', function() {
                    currentStageIndex = index;
                });
            });

            // 添加键盘导航支持
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft' && currentStageIndex > 0) {
                    currentStageIndex--;
                    const targetButton = document.querySelector(`[data-stage="${stages[currentStageIndex]}"]`);
                    targetButton.click();
                } else if (e.key === 'ArrowRight' && currentStageIndex < stages.length - 1) {
                    currentStageIndex++;
                    const targetButton = document.querySelector(`[data-stage="${stages[currentStageIndex]}"]`);
                    targetButton.click();
                }
            });

            // 添加滚动时的视差效果
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const header = document.querySelector('.header');

                if (header) {
                    header.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });

            // 添加卡片进入动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 初始化卡片动画
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });

            // 添加提示标签点击效果
            const tips = document.querySelectorAll('.tip');
            tips.forEach(tip => {
                tip.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    this.style.background = 'rgba(255,255,255,0.4)';

                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                        this.style.background = 'rgba(255,255,255,0.2)';
                    }, 150);
                });
            });

            // 初始化当前阶段索引
            currentStageIndex = getCurrentStageIndex();

            // 添加页面加载完成后的欢迎动画
            setTimeout(() => {
                const header = document.querySelector('.header');
                header.style.animation = 'pulse 2s ease-in-out';
            }, 500);
        });
    </script>
</body>
</html>
