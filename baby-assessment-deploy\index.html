<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>婴儿认知发展月度评估</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            padding: 2rem 1rem;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .month-selector {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 2px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .month-nav {
            display: flex;
            overflow-x: auto;
            gap: 0.5rem;
            padding: 0.5rem 0;
        }

        .month-btn {
            min-width: 60px;
            padding: 0.8rem 1rem;
            border: none;
            background: white;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .month-btn:hover {
            background: #e9ecef;
            color: #495057;
            transform: translateY(-2px);
        }

        .month-btn.active {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        .content {
            padding: 1.5rem;
        }

        .month-content {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .month-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .month-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .month-header h2 {
            font-size: 1.6rem;
            margin-bottom: 0.5rem;
        }

        .month-header .age-info {
            font-size: 1rem;
            opacity: 0.9;
        }

        .assessment-section {
            margin-bottom: 2rem;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }

        .assessment-items {
            display: grid;
            gap: 1rem;
        }

        .assessment-item {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .assessment-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .item-title {
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
        }

        .assessment-checkbox {
            display: flex;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        .checkbox-option {
            position: relative;
        }

        .checkbox-option input[type="radio"] {
            display: none;
        }

        .checkbox-option label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .checkbox-option.achieved label {
            background: #e8f5e8;
            color: #28a745;
            border: 2px solid #28a745;
        }

        .checkbox-option.partial label {
            background: #fff3cd;
            color: #ffc107;
            border: 2px solid #ffc107;
        }

        .checkbox-option.not-achieved label {
            background: #f8d7da;
            color: #dc3545;
            border: 2px solid #dc3545;
        }

        .checkbox-option input[type="radio"]:checked + label {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .item-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .progress-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 1.5rem;
            color: white;
            margin-bottom: 2rem;
            text-align: center;
        }

        .progress-bar {
            background: rgba(255,255,255,0.3);
            border-radius: 10px;
            height: 20px;
            margin: 1rem 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .suggestions {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .suggestions h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .suggestion-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #17a2b8;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .report-summary {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            border: 1px solid #e9ecef;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .summary-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }

        .summary-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .header {
                padding: 1.5rem 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .month-nav {
                justify-content: flex-start;
                padding-bottom: 1rem;
            }

            .month-btn {
                min-width: 50px;
                padding: 0.6rem 0.8rem;
                font-size: 0.8rem;
            }

            .content {
                padding: 1rem;
            }

            .assessment-item {
                padding: 1rem;
            }

            .item-header {
                flex-direction: column;
                gap: 1rem;
                align-items: center;
                text-align: center;
            }

            .assessment-checkbox {
                justify-content: center;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .summary-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }

            .month-header h2 {
                font-size: 1.3rem;
            }

            .section-title {
                font-size: 1.1rem;
                text-align: center;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.3rem;
            }

            .month-btn {
                min-width: 45px;
                padding: 0.5rem 0.6rem;
                font-size: 0.75rem;
            }

            .content {
                padding: 0.5rem;
            }

            .month-header {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .assessment-item {
                padding: 0.8rem;
                margin-bottom: 0.8rem;
            }

            .checkbox-option label {
                width: 30px;
                height: 30px;
                font-size: 0.7rem;
            }

            .summary-grid {
                grid-template-columns: 1fr;
            }

            .summary-item {
                padding: 0.8rem;
            }

            .summary-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>👶 婴儿认知发展月度评估</h1>
            <p class="subtitle">科学评估宝宝每个月的发展里程碑</p>
        </header>

        <div class="month-selector">
            <div class="month-nav" id="monthNav">
                <!-- 月份按钮将通过JavaScript生成 -->
            </div>
        </div>

        <main class="content">
            <div class="progress-summary">
                <h3>📊 本月发展评估进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <p id="progressText">请开始评估宝宝的发展情况</p>
            </div>

            <div id="monthContent">
                <!-- 月份内容将通过JavaScript生成 -->
            </div>

            <div class="action-buttons">
                <button class="action-btn btn-primary" onclick="generateReport()">
                    📊 生成评估报告
                </button>
                <button class="action-btn btn-secondary" onclick="exportData()">
                    💾 导出数据
                </button>
                <button class="action-btn btn-success" onclick="clearData()">
                    🔄 重置评估
                </button>
            </div>

            <div class="report-summary" id="reportSummary" style="display: none;">
                <h4>📈 评估总结报告</h4>
                <div class="summary-grid" id="summaryGrid">
                    <!-- 报告内容将通过JavaScript生成 -->
                </div>
            </div>

            <div class="suggestions" id="suggestions" style="display: none;">
                <h4>💡 个性化建议</h4>
                <div id="suggestionContent">
                    <!-- 建议内容将根据评估结果生成 -->
                </div>
            </div>
        </main>
    </div>

    <script>
        // 评估数据结构
        const assessmentData = {
            1: {
                title: "1个月宝宝发展评估",
                ageInfo: "新生儿期 - 适应期",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "视觉注意力",
                                description: "能够短暂注视人脸或高对比度物体（20-30cm距离）",
                                suggestions: ["使用黑白对比强烈的图片", "保持眼神交流", "缓慢移动物体让宝宝追视"]
                            },
                            {
                                title: "听觉反应",
                                description: "对突然的声音有惊跳反应，喜欢熟悉的声音",
                                suggestions: ["轻柔地与宝宝说话", "播放轻柔音乐", "模仿宝宝的声音"]
                            },
                            {
                                title: "触觉感知",
                                description: "对触摸有反应，喜欢温暖柔软的接触",
                                suggestions: ["温柔的抚触按摩", "不同质地的布料接触", "皮肤接触时间"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "原始反射",
                                description: "具有完整的原始反射（吸吮、握持、惊跳反射等）",
                                suggestions: ["观察反射是否正常", "轻触手心观察握持反射", "注意反射的对称性"]
                            },
                            {
                                title: "头部控制",
                                description: "俯卧时能短暂抬头（几秒钟）",
                                suggestions: ["每天进行俯卧练习", "用玩具吸引抬头", "逐渐增加俯卧时间"]
                            },
                            {
                                title: "肌肉张力",
                                description: "四肢肌肉张力正常，无过度紧张或松弛",
                                suggestions: ["观察宝宝的姿势", "轻柔的被动运动", "注意肌肉张力变化"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "发声反应",
                                description: "会发出简单的声音，如"啊"、"呜"等",
                                suggestions: ["回应宝宝的声音", "模仿宝宝发音", "用不同音调说话"]
                            },
                            {
                                title: "哭声分化",
                                description: "不同需求时哭声有所不同（饿、困、不适）",
                                suggestions: ["学习识别不同哭声", "及时回应宝宝需求", "观察哭声模式"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "注视人脸",
                                description: "喜欢看人的脸，特别是妈妈的脸",
                                suggestions: ["多进行面对面交流", "保持眼神接触", "做夸张的面部表情"]
                            },
                            {
                                title: "安抚反应",
                                description: "听到熟悉声音或被抱起时能够安静下来",
                                suggestions: ["建立规律的安抚方式", "使用一致的声音", "温柔的摇摆动作"]
                            }
                        ]
                    }
                ]
            },
            2: {
                title: "2个月宝宝发展评估",
                ageInfo: "快速发展期 - 社交萌芽",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "视觉追踪",
                                description: "能够追踪缓慢移动的物体，视线可跟随180度",
                                suggestions: ["用彩色玩具练习追视", "左右移动物体", "增加追视距离"]
                            },
                            {
                                title: "注意力持续",
                                description: "能够持续注视感兴趣的物体30秒以上",
                                suggestions: ["提供有趣的视觉刺激", "观察宝宝的兴趣点", "避免过度刺激"]
                            },
                            {
                                title: "记忆萌芽",
                                description: "开始对熟悉的人和环境表现出认知",
                                suggestions: ["保持环境的一致性", "重复日常活动", "建立固定的作息"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "头部控制增强",
                                description: "俯卧时能抬头45度，持续时间更长",
                                suggestions: ["增加俯卧时间", "用玩具鼓励抬头", "支撑练习"]
                            },
                            {
                                title: "手部动作",
                                description: "握持反射开始减弱，手指开始放松",
                                suggestions: ["轻抚手心", "提供不同触感物品", "观察手部变化"]
                            },
                            {
                                title: "腿部活动",
                                description: "仰卧时腿部活动更加协调有力",
                                suggestions: ["鼓励踢腿运动", "轻柔的腿部按摩", "提供踢踏玩具"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "咕咕声",
                                description: "开始发出咕咕声、咯咯声等愉快的声音",
                                suggestions: ["回应宝宝的声音", "模仿发音", "创造对话氛围"]
                            },
                            {
                                title: "声音定位",
                                description: "听到声音时会转向声源方向",
                                suggestions: ["从不同方向发出声音", "使用摇铃等玩具", "观察反应方向"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "社交性微笑",
                                description: "开始出现真正的社交性微笑，不只是生理性微笑",
                                suggestions: ["多与宝宝互动", "做夸张表情", "及时回应微笑"]
                            },
                            {
                                title: "情绪表达",
                                description: "开始表现出愉快、不满等基本情绪",
                                suggestions: ["观察情绪变化", "及时回应情绪", "创造愉快氛围"]
                            }
                        ]
                    }
                ]
            },
            3: {
                title: "3个月宝宝发展评估",
                ageInfo: "社交发展期 - 互动增强",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "视觉协调",
                                description: "双眼能够协调追踪物体，开始有深度知觉",
                                suggestions: ["提供立体玩具", "练习远近距离追视", "使用彩色移动玩具"]
                            },
                            {
                                title: "手眼协调萌芽",
                                description: "开始注意自己的手，偶尔能够碰到悬挂的玩具",
                                suggestions: ["悬挂适当高度的玩具", "鼓励手部探索", "提供安全的抓握物品"]
                            },
                            {
                                title: "记忆发展",
                                description: "能够记住熟悉的人和日常活动模式",
                                suggestions: ["建立固定作息", "重复熟悉的活动", "保持环境一致性"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "头部控制完善",
                                description: "俯卧时能稳定抬头90度，竖抱时头部稳定",
                                suggestions: ["增加俯卧练习时间", "竖抱练习", "头部支撑游戏"]
                            },
                            {
                                title: "翻身准备",
                                description: "侧卧时能保持平衡，开始尝试翻身动作",
                                suggestions: ["侧卧位练习", "用玩具引导翻身", "提供安全的练习空间"]
                            },
                            {
                                title: "手部控制",
                                description: "握持反射消失，开始主动张开和握紧手掌",
                                suggestions: ["提供不同质地物品", "手指游戏", "抓握练习"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "发音丰富",
                                description: "能发出更多种类的声音，开始咿呀学语",
                                suggestions: ["模仿宝宝发音", "进行声音对话", "唱歌给宝宝听"]
                            },
                            {
                                title: "声音识别",
                                description: "能够识别并回应熟悉的声音",
                                suggestions: ["用名字呼唤宝宝", "重复使用相同词汇", "观察声音反应"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "主动微笑",
                                description: "会主动对人微笑，特别是对熟悉的人",
                                suggestions: ["多与宝宝互动", "回应宝宝微笑", "创造愉快氛围"]
                            },
                            {
                                title: "情绪表达清晰",
                                description: "高兴、不满、兴奋等情绪表达更加明显",
                                suggestions: ["观察情绪变化", "及时回应情绪", "建立情感连接"]
                            }
                        ]
                    }
                ]
            },
            4: {
                title: "4个月宝宝发展评估",
                ageInfo: "探索期 - 主动互动",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "因果关系理解",
                                description: "开始理解简单的因果关系，如摇动玩具会发声",
                                suggestions: ["提供因果关系玩具", "演示动作结果", "鼓励重复动作"]
                            },
                            {
                                title: "物体恒存性萌芽",
                                description: "当物体消失时会寻找，但时间很短",
                                suggestions: ["玩简单的躲猫猫", "部分遮挡玩具", "观察寻找行为"]
                            },
                            {
                                title: "注意力集中",
                                description: "能够专注于感兴趣的活动更长时间",
                                suggestions: ["提供有趣的视觉刺激", "避免过度刺激", "观察兴趣点"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "翻身能力",
                                description: "能够从仰卧翻到俯卧，或从俯卧翻到仰卧",
                                suggestions: ["提供翻身练习空间", "用玩具引导", "确保安全环境"]
                            },
                            {
                                title: "坐立准备",
                                description: "有支撑时能短时间坐立，头部和躯干控制改善",
                                suggestions: ["支撑坐立练习", "使用婴儿座椅", "逐渐减少支撑"]
                            },
                            {
                                title: "抓握发展",
                                description: "能够主动抓握物品，开始用整个手掌抓握",
                                suggestions: ["提供适合抓握的玩具", "不同大小的物品", "安全的探索环境"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "音节发展",
                                description: "开始发出重复的音节，如\"ba-ba\"、\"da-da\"",
                                suggestions: ["重复宝宝的音节", "教授简单音节", "进行音节游戏"]
                            },
                            {
                                title: "语调变化",
                                description: "发声时有不同的语调和节奏变化",
                                suggestions: ["用不同语调说话", "唱歌互动", "模仿语调变化"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "大笑表现",
                                description: "会发出咯咯笑声，特别是在游戏时",
                                suggestions: ["进行有趣的游戏", "做夸张动作", "创造欢乐氛围"]
                            },
                            {
                                title: "陌生人反应",
                                description: "开始对陌生人表现出好奇或谨慎",
                                suggestions: ["逐渐介绍新面孔", "在熟悉环境中接触", "观察反应模式"]
                            }
                        ]
                    }
                ]
            },
            5: {
                title: "5个月宝宝发展评估",
                ageInfo: "坐立发展期 - 探索增强",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "物体恒存性发展",
                                description: "当物体被遮挡时会寻找，表现出物体恒存性的初步理解",
                                suggestions: ["玩躲猫猫游戏", "部分遮挡玩具观察反应", "使用透明容器装玩具"]
                            },
                            {
                                title: "手眼协调",
                                description: "能够准确抓取眼前的物体，手眼协调能力明显提升",
                                suggestions: ["提供不同大小的安全玩具", "练习抓取游戏", "使用彩色积木"]
                            },
                            {
                                title: "注意力集中",
                                description: "能够专注于一个活动5-10分钟",
                                suggestions: ["提供有趣但不过度刺激的玩具", "观察宝宝的兴趣点", "避免环境干扰"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "坐立能力",
                                description: "能够在有支撑的情况下稳定坐立，头部和躯干控制良好",
                                suggestions: ["使用靠垫支撑练习坐立", "逐渐减少支撑", "确保安全的练习环境"]
                            },
                            {
                                title: "翻身熟练",
                                description: "能够熟练地从仰卧翻到俯卧，并能翻回来",
                                suggestions: ["提供足够的翻身空间", "用玩具引导翻身方向", "确保地面安全"]
                            },
                            {
                                title: "抓握精细化",
                                description: "开始使用拇指和其他手指配合抓取小物体",
                                suggestions: ["提供适合抓握的小玩具", "练习转移物体", "注意安全避免误食"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "音节组合",
                                description: "能够发出更复杂的音节组合，如\"ba-ba-ba\"、\"da-da-da\"",
                                suggestions: ["重复宝宝的音节", "教授新的音节", "进行音节对话"]
                            },
                            {
                                title: "声音模仿",
                                description: "开始模仿听到的声音和语调",
                                suggestions: ["做不同的声音给宝宝听", "模仿动物叫声", "用夸张的语调说话"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "社交互动",
                                description: "主动寻求与人互动，喜欢被关注",
                                suggestions: ["多与宝宝进行面对面交流", "回应宝宝的社交信号", "创造互动机会"]
                            },
                            {
                                title: "情绪表达丰富",
                                description: "能够表达多种情绪，如高兴、不满、好奇等",
                                suggestions: ["观察并回应宝宝的情绪", "用语言描述宝宝的情绪", "建立情感连接"]
                            }
                        ]
                    }
                ]
            },
            6: {
                title: "6个月宝宝发展评估",
                ageInfo: "辅食期 - 独立坐立",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "因果关系理解",
                                description: "明确理解动作与结果的关系，会重复产生有趣结果的动作",
                                suggestions: ["提供因果关系明显的玩具", "演示动作和结果", "鼓励重复尝试"]
                            },
                            {
                                title: "空间认知",
                                description: "开始理解物体的空间关系，如上下、里外",
                                suggestions: ["玩容器游戏", "练习放入取出", "使用不同大小的容器"]
                            },
                            {
                                title: "记忆发展",
                                description: "能够记住短期内发生的事情，表现出期待行为",
                                suggestions: ["建立固定的日常程序", "重复熟悉的游戏", "观察期待反应"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "独立坐立",
                                description: "能够不需要支撑独立坐立，保持平衡",
                                suggestions: ["练习无支撑坐立", "在安全环境中练习", "逐渐增加坐立时间"]
                            },
                            {
                                title: "爬行准备",
                                description: "俯卧时能够用手臂支撑身体，开始准备爬行",
                                suggestions: ["鼓励俯卧时间", "用玩具引导向前", "提供爬行空间"]
                            },
                            {
                                title: "双手协调",
                                description: "能够用双手同时操作物体，手部协调性提高",
                                suggestions: ["提供需要双手操作的玩具", "练习传递物体", "鼓励双手探索"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "辅音发展",
                                description: "开始发出更多辅音，如\"m\"、\"b\"、\"d\"等",
                                suggestions: ["重复宝宝的发音", "教授新的辅音", "进行发音游戏"]
                            },
                            {
                                title: "语调变化",
                                description: "发声时有明显的语调起伏，像在\"说话\"",
                                suggestions: ["用不同语调与宝宝对话", "模仿宝宝的语调", "创造对话氛围"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "陌生人焦虑",
                                description: "开始对陌生人表现出谨慎或焦虑",
                                suggestions: ["逐渐介绍新面孔", "在熟悉环境中接触陌生人", "给予安全感"]
                            },
                            {
                                title: "依恋行为",
                                description: "对主要照顾者表现出明显的依恋行为",
                                suggestions: ["建立稳定的照顾关系", "及时回应宝宝需求", "提供安全感"]
                            }
                        ]
                    }
                ]
            },
            7: {
                title: "7个月宝宝发展评估",
                ageInfo: "爬行准备期 - 探索欲增强",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "物体恒存性强化",
                                description: "当物体完全消失时会积极寻找，物体恒存性概念更加稳固",
                                suggestions: ["玩更复杂的躲猫猫", "完全遮挡物体观察反应", "使用多层遮挡游戏"]
                            },
                            {
                                title: "探索行为",
                                description: "对周围环境表现出强烈的探索欲望，喜欢触摸和操作物体",
                                suggestions: ["提供安全的探索环境", "不同质地的物品", "鼓励触觉探索"]
                            },
                            {
                                title: "模仿学习",
                                description: "开始模仿简单的动作和声音",
                                suggestions: ["做简单动作让宝宝模仿", "拍手游戏", "模仿面部表情"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "爬行动作",
                                description: "开始出现爬行动作，可能是腹部贴地的匍匐爬行",
                                suggestions: ["提供爬行练习空间", "用玩具引导爬行", "确保地面安全舒适"]
                            },
                            {
                                title: "坐立稳定",
                                description: "坐立更加稳定，能够在坐立时玩玩具",
                                suggestions: ["在坐立时提供玩具", "练习坐立平衡", "逐渐增加坐立时间"]
                            },
                            {
                                title: "精细动作",
                                description: "能够用拇指和食指捏取小物体（钳形抓握）",
                                suggestions: ["提供小而安全的物品", "练习捏取动作", "注意安全防止误食"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "音节重复",
                                description: "能够重复发出相同的音节，如\"ba-ba\"、\"ma-ma\"",
                                suggestions: ["重复宝宝的音节", "教授\"妈妈\"\"爸爸\"", "进行音节对话"]
                            },
                            {
                                title: "语音理解",
                                description: "开始理解一些简单的词汇，如自己的名字",
                                suggestions: ["经常叫宝宝的名字", "使用简单词汇", "观察理解反应"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "分离焦虑萌芽",
                                description: "当主要照顾者离开时可能表现出不安",
                                suggestions: ["逐渐练习短暂分离", "建立离别仪式", "给予安全感"]
                            },
                            {
                                title: "社交游戏",
                                description: "喜欢参与简单的社交游戏，如拍手、躲猫猫",
                                suggestions: ["进行互动游戏", "拍手歌", "简单的社交活动"]
                            }
                        ]
                    }
                ]
            },
            8: {
                title: "8个月宝宝发展评估",
                ageInfo: "爬行期 - 分离焦虑期",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "目标导向行为",
                                description: "为了达到目标会采取一系列行动，如爬向想要的玩具",
                                suggestions: ["设置有趣的目标", "鼓励解决问题", "提供挑战性任务"]
                            },
                            {
                                title: "空间概念发展",
                                description: "理解物体的空间关系，如里面、外面、上面、下面",
                                suggestions: ["玩空间游戏", "使用容器玩具", "练习放入取出"]
                            },
                            {
                                title: "记忆持续时间",
                                description: "短期记忆能力增强，能记住更长时间的事件",
                                suggestions: ["建立日常程序", "重复熟悉活动", "观察记忆表现"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "熟练爬行",
                                description: "能够熟练爬行，可能开始手膝爬行",
                                suggestions: ["提供爬行障碍", "创造爬行游戏", "确保环境安全"]
                            },
                            {
                                title: "拉站准备",
                                description: "开始尝试拉着家具站立",
                                suggestions: ["提供安全的支撑物", "鼓励拉站动作", "确保家具稳固"]
                            },
                            {
                                title: "手指灵活性",
                                description: "手指动作更加灵活，能够操作小物体",
                                suggestions: ["提供精细动作玩具", "练习手指游戏", "安全的小物品操作"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "有意义发音",
                                description: "\"妈妈\"\"爸爸\"可能开始有特定指向性",
                                suggestions: ["强化\"妈妈\"\"爸爸\"的使用", "及时回应", "鼓励正确使用"]
                            },
                            {
                                title: "手势交流",
                                description: "开始使用手势进行交流，如挥手再见",
                                suggestions: ["教授简单手势", "挥手再见", "拍手表示高兴"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "分离焦虑",
                                description: "对主要照顾者的离开表现出明显的焦虑和不安",
                                suggestions: ["逐渐练习分离", "建立安全感", "使用过渡物品"]
                            },
                            {
                                title: "陌生人警惕",
                                description: "对陌生人表现出明显的警惕和回避",
                                suggestions: ["在熟悉环境中接触新人", "给予充分时间适应", "不强迫互动"]
                            }
                        ]
                    }
                ]
            },
            9: {
                title: "9个月宝宝发展评估",
                ageInfo: "站立准备期 - 精细动作发展",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "工具使用",
                                description: "开始使用简单工具达到目标，如用棍子够取远处物体",
                                suggestions: ["提供安全的工具类玩具", "演示工具使用", "鼓励创新使用"]
                            },
                            {
                                title: "分类概念",
                                description: "开始理解物体的分类，能够将相似物体放在一起",
                                suggestions: ["提供分类游戏", "按颜色形状分类", "简单的配对活动"]
                            },
                            {
                                title: "问题解决",
                                description: "面对障碍时会尝试不同方法解决问题",
                                suggestions: ["设置简单障碍", "鼓励尝试不同方法", "给予解决问题的机会"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "拉站能力",
                                description: "能够拉着家具站立，并保持短时间平衡",
                                suggestions: ["提供稳固的支撑", "练习拉站动作", "逐渐增加站立时间"]
                            },
                            {
                                title: "侧步移动",
                                description: "扶着家具可以侧步移动",
                                suggestions: ["沿着家具放置玩具", "鼓励侧步移动", "确保路径安全"]
                            },
                            {
                                title: "钳形抓握",
                                description: "拇指和食指的钳形抓握更加精确",
                                suggestions: ["提供小物品练习", "捡拾游戏", "注意安全防误食"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇理解",
                                description: "能够理解更多词汇，如\"不\"、\"来\"、\"拜拜\"",
                                suggestions: ["使用简单指令", "重复常用词汇", "观察理解反应"]
                            },
                            {
                                title: "手势语言",
                                description: "手势交流更加丰富，如指向、挥手、拍手",
                                suggestions: ["教授更多手势", "结合语言和手势", "鼓励手势表达"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "社交参照",
                                description: "遇到不确定情况时会看照顾者的反应",
                                suggestions: ["给予积极的面部表情", "用表情传达安全感", "建立信任关系"]
                            },
                            {
                                title: "游戏互动",
                                description: "喜欢与人进行互动游戏，如传球、躲猫猫",
                                suggestions: ["进行互动游戏", "轮流游戏", "建立游戏规则"]
                            }
                        ]
                    }
                ]
            },
            10: {
                title: "10个月宝宝发展评估",
                ageInfo: "模仿学习期 - 独立性增强",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "模仿复杂动作",
                                description: "能够模仿更复杂的动作序列",
                                suggestions: ["演示多步骤动作", "分解复杂动作", "鼓励模仿学习"]
                            },
                            {
                                title: "容器概念",
                                description: "理解容器的概念，喜欢放入和取出游戏",
                                suggestions: ["提供各种容器", "放入取出游戏", "不同大小的物品"]
                            },
                            {
                                title: "预期行为",
                                description: "对熟悉的活动序列表现出预期行为",
                                suggestions: ["建立固定程序", "观察预期反应", "强化程序概念"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "巡航走路",
                                description: "能够扶着家具\"巡航\"移动，准备独立行走",
                                suggestions: ["创造巡航路径", "在家具间放置玩具", "鼓励移动探索"]
                            },
                            {
                                title: "独立站立",
                                description: "可能开始尝试短暂的独立站立",
                                suggestions: ["鼓励独立站立", "提供安全环境", "逐渐增加站立时间"]
                            },
                            {
                                title: "精细动作协调",
                                description: "双手协调性更好，能够同时使用双手完成任务",
                                suggestions: ["双手协调游戏", "需要双手的玩具", "练习协调动作"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "第一个词",
                                description: "可能说出第一个有意义的词汇",
                                suggestions: ["重复简单词汇", "及时回应发音", "鼓励语言尝试"]
                            },
                            {
                                title: "语音模仿",
                                description: "更准确地模仿听到的语音",
                                suggestions: ["清晰地发音", "重复宝宝的尝试", "进行语音游戏"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "情感表达",
                                description: "情感表达更加丰富和明确",
                                suggestions: ["回应宝宝的情感", "用语言描述情感", "建立情感词汇"]
                            },
                            {
                                title: "独立性",
                                description: "开始表现出独立性，想要自己做事情",
                                suggestions: ["给予适当的独立机会", "鼓励自主尝试", "提供安全的探索环境"]
                            }
                        ]
                    }
                ]
            },
            11: {
                title: "11个月宝宝发展评估",
                ageInfo: "语言萌芽期 - 行走准备",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "符号理解",
                                description: "开始理解简单的符号和手势的含义",
                                suggestions: ["使用简单手势", "结合语言和动作", "建立符号系统"]
                            },
                            {
                                title: "序列记忆",
                                description: "能够记住简单的动作序列",
                                suggestions: ["教授简单序列", "重复日常程序", "观察记忆表现"]
                            },
                            {
                                title: "探索策略",
                                description: "探索物体时使用多种策略，如摇晃、敲击、投掷",
                                suggestions: ["提供可探索的安全物品", "鼓励不同探索方式", "观察探索模式"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "独立行走准备",
                                description: "可能开始尝试独立迈出几步",
                                suggestions: ["鼓励独立行走", "提供安全的练习空间", "使用推拉玩具"]
                            },
                            {
                                title: "攀爬能力",
                                description: "开始尝试攀爬低矮的物体",
                                suggestions: ["提供安全的攀爬机会", "软垫保护", "监督攀爬活动"]
                            },
                            {
                                title: "精细动作发展",
                                description: "能够完成更精细的手部动作，如翻书页",
                                suggestions: ["提供翻页书籍", "精细动作玩具", "练习小肌肉控制"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇增长",
                                description: "词汇量开始快速增长，可能有2-3个清晰的词",
                                suggestions: ["重复新词汇", "扩展词汇使用", "创造语言环境"]
                            },
                            {
                                title: "语言理解",
                                description: "能够理解简单的指令和问题",
                                suggestions: ["使用简单指令", "问简单问题", "观察理解程度"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "情感调节",
                                description: "开始学习调节自己的情绪",
                                suggestions: ["帮助识别情绪", "教授安抚方法", "建立情绪词汇"]
                            },
                            {
                                title: "社交技能",
                                description: "社交技能更加复杂，能够进行简单的社交互动",
                                suggestions: ["创造社交机会", "教授社交技能", "鼓励与他人互动"]
                            }
                        ]
                    }
                ]
            },
            12: {
                title: "12个月宝宝发展评估",
                ageInfo: "周岁里程碑 - 语言爆发前期",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "物体功能理解",
                                description: "开始理解物体的功能，如杯子用来喝水",
                                suggestions: ["演示物体功能", "功能性游戏", "日常生活中的学习"]
                            },
                            {
                                title: "简单问题解决",
                                description: "能够解决简单的问题，如绕过障碍物",
                                suggestions: ["设置简单挑战", "鼓励独立解决", "提供解决线索"]
                            },
                            {
                                title: "模仿学习增强",
                                description: "模仿能力显著增强，能够模仿日常活动",
                                suggestions: ["演示日常活动", "鼓励模仿", "提供模仿机会"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "独立行走",
                                description: "大多数宝宝能够独立行走几步到十几步",
                                suggestions: ["鼓励行走练习", "创造行走动机", "确保安全环境"]
                            },
                            {
                                title: "精细动作精确",
                                description: "精细动作更加精确，能够完成复杂的手部任务",
                                suggestions: ["提供精细动作挑战", "练习手眼协调", "使用小物品游戏"]
                            },
                            {
                                title: "身体协调",
                                description: "整体身体协调性显著提高",
                                suggestions: ["全身运动游戏", "平衡练习", "协调性训练"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "第一个词汇",
                                description: "大多数宝宝能够说出第一个有意义的词汇",
                                suggestions: ["庆祝第一个词", "重复和扩展", "鼓励语言尝试"]
                            },
                            {
                                title: "语言理解飞跃",
                                description: "语言理解能力有显著提升",
                                suggestions: ["丰富语言输入", "使用描述性语言", "回应理解表现"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "依恋安全感",
                                description: "与主要照顾者建立稳定的依恋关系",
                                suggestions: ["建立安全依恋", "及时回应需求", "提供情感支持"]
                            },
                            {
                                title: "社交兴趣",
                                description: "对其他人特别是同龄儿童表现出兴趣",
                                suggestions: ["创造社交机会", "观察社交行为", "鼓励友好互动"]
                            }
                        ]
                    }
                ]
            },
            13: {
                title: "13个月宝宝发展评估",
                ageInfo: "幼儿期开始 - 行走稳定期",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "功能性游戏",
                                description: "开始进行功能性游戏，如假装喝茶、打电话",
                                suggestions: ["提供角色扮演玩具", "参与假想游戏", "鼓励创造性游戏"]
                            },
                            {
                                title: "因果关系掌握",
                                description: "更好地理解因果关系，能够预测结果",
                                suggestions: ["因果关系游戏", "解释行为结果", "预测游戏"]
                            },
                            {
                                title: "记忆发展",
                                description: "短期记忆显著改善，能记住更多信息",
                                suggestions: ["记忆游戏", "重复故事", "建立记忆策略"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "稳定行走",
                                description: "行走更加稳定，很少跌倒",
                                suggestions: ["鼓励多走路", "不同地面练习", "提高平衡能力"]
                            },
                            {
                                title: "跑步尝试",
                                description: "开始尝试跑步，虽然还不够协调",
                                suggestions: ["安全的跑步空间", "追逐游戏", "逐渐提高速度"]
                            },
                            {
                                title: "精细动作进步",
                                description: "能够完成更复杂的精细动作任务",
                                suggestions: ["拼图游戏", "积木搭建", "绘画涂鸦"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇快速增长",
                                description: "词汇量快速增长，可能达到5-10个词",
                                suggestions: ["丰富词汇输入", "重复新词", "创造使用机会"]
                            },
                            {
                                title: "简单指令理解",
                                description: "能够理解和执行简单的一步指令",
                                suggestions: ["给予简单指令", "逐步增加复杂度", "及时表扬执行"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "独立性增强",
                                description: "表现出更强的独立性，想要自己做事",
                                suggestions: ["给予适当独立机会", "鼓励自主尝试", "耐心等待完成"]
                            },
                            {
                                title: "情绪表达清晰",
                                description: "情绪表达更加清晰和多样化",
                                suggestions: ["识别和命名情绪", "教授情绪管理", "回应情绪需求"]
                            }
                        ]
                    }
                ]
            },
            14: {
                title: "14个月宝宝发展评估",
                ageInfo: "探索期 - 语言理解飞跃",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "象征性思维",
                                description: "开始发展象征性思维，理解物体可以代表其他事物",
                                suggestions: ["象征性游戏", "使用替代物品", "鼓励想象力"]
                            },
                            {
                                title: "空间关系理解",
                                description: "更好地理解空间关系，如上下、里外、前后",
                                suggestions: ["空间概念游戏", "方位词教学", "空间探索活动"]
                            },
                            {
                                title: "注意力持续",
                                description: "注意力持续时间延长，能专注更长时间",
                                suggestions: ["适龄的专注活动", "逐渐延长活动时间", "避免过度刺激"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "协调性提高",
                                description: "整体运动协调性显著提高",
                                suggestions: ["协调性训练", "平衡游戏", "全身运动活动"]
                            },
                            {
                                title: "攀爬技能",
                                description: "攀爬技能发展，能够爬上爬下",
                                suggestions: ["安全的攀爬设施", "监督攀爬活动", "逐渐增加难度"]
                            },
                            {
                                title: "手部灵活性",
                                description: "手部动作更加灵活精确",
                                suggestions: ["精细动作练习", "手工活动", "操作性玩具"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "语言理解跳跃",
                                description: "语言理解能力有显著跳跃式发展",
                                suggestions: ["丰富语言环境", "复杂句式输入", "观察理解程度"]
                            },
                            {
                                title: "表达尝试增加",
                                description: "尝试表达的频率和复杂度增加",
                                suggestions: ["鼓励表达尝试", "耐心倾听", "扩展表达内容"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "社交兴趣增强",
                                description: "对社交互动表现出更大兴趣",
                                suggestions: ["创造社交机会", "教授社交技能", "观察社交行为"]
                            },
                            {
                                title: "模仿行为复杂",
                                description: "模仿行为变得更加复杂和有目的",
                                suggestions: ["提供模仿机会", "日常活动模仿", "鼓励创新模仿"]
                            }
                        ]
                    }
                ]
            },
            15: {
                title: "15个月宝宝发展评估",
                ageInfo: "语言爆发期 - 自主性发展",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "分类能力",
                                description: "能够按照简单特征对物体进行分类",
                                suggestions: ["分类游戏", "按颜色形状分类", "日常物品分类"]
                            },
                            {
                                title: "序列理解",
                                description: "开始理解简单的序列和顺序概念",
                                suggestions: ["序列游戏", "日常程序教学", "故事序列"]
                            },
                            {
                                title: "问题解决策略",
                                description: "解决问题时使用更多样的策略",
                                suggestions: ["提供问题解决机会", "鼓励尝试不同方法", "引导思考过程"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "跑步协调",
                                description: "跑步动作更加协调，很少跌倒",
                                suggestions: ["跑步练习", "追逐游戏", "不同速度练习"]
                            },
                            {
                                title: "精细动作精确",
                                description: "精细动作更加精确，能够完成复杂任务",
                                suggestions: ["精细动作挑战", "手工制作", "绘画活动"]
                            },
                            {
                                title: "身体控制",
                                description: "对身体的控制能力显著提高",
                                suggestions: ["身体控制游戏", "平衡练习", "协调性训练"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇爆发",
                                description: "词汇量快速增长，可能达到10-20个词",
                                suggestions: ["持续词汇输入", "重复和扩展", "创造使用情境"]
                            },
                            {
                                title: "两词组合",
                                description: "开始尝试将两个词组合使用",
                                suggestions: ["示范两词组合", "鼓励组合尝试", "扩展表达"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "自主性表现",
                                description: "强烈表现出自主性，经常说\"不\"",
                                suggestions: ["理解自主需求", "提供选择机会", "设定合理界限"]
                            },
                            {
                                title: "情感连接",
                                description: "与照顾者的情感连接更加深入",
                                suggestions: ["加强情感交流", "回应情感需求", "建立安全感"]
                            }
                        ]
                    }
                ]
            },
            16: {
                title: "16个月宝宝发展评估",
                ageInfo: "创造力萌芽期 - 社交技能发展",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "创造性思维",
                                description: "开始表现出创造性思维，用物品进行创新游戏",
                                suggestions: ["开放性玩具", "鼓励创新使用", "支持创造性探索"]
                            },
                            {
                                title: "记忆策略",
                                description: "开始使用简单的记忆策略",
                                suggestions: ["记忆游戏", "重复练习", "建立记忆线索"]
                            },
                            {
                                title: "抽象概念初步",
                                description: "开始理解一些抽象概念，如大小、多少",
                                suggestions: ["概念教学", "比较游戏", "日常概念应用"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "复杂动作序列",
                                description: "能够完成更复杂的动作序列",
                                suggestions: ["多步骤动作练习", "舞蹈动作", "运动游戏"]
                            },
                            {
                                title: "工具使用",
                                description: "更熟练地使用各种工具",
                                suggestions: ["提供安全工具", "工具使用练习", "日常工具体验"]
                            },
                            {
                                title: "空间导航",
                                description: "在熟悉环境中的空间导航能力提高",
                                suggestions: ["空间探索游戏", "方向感训练", "环境认知"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "语法萌芽",
                                description: "开始表现出简单的语法规则理解",
                                suggestions: ["正确语法示范", "语法游戏", "句式练习"]
                            },
                            {
                                title: "交流意图",
                                description: "交流意图更加明确和复杂",
                                suggestions: ["理解交流意图", "回应交流尝试", "扩展交流内容"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "同理心萌芽",
                                description: "开始表现出同理心的萌芽",
                                suggestions: ["情感教育", "同理心示范", "关爱行为鼓励"]
                            },
                            {
                                title: "社交规则理解",
                                description: "开始理解简单的社交规则",
                                suggestions: ["社交规则教学", "示范社交行为", "练习社交技能"]
                            }
                        ]
                    }
                ]
            },
            17: {
                title: "17个月宝宝发展评估",
                ageInfo: "语言快速发展期 - 独立性增强",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "符号表征",
                                description: "理解符号可以代表真实物体或概念",
                                suggestions: ["符号游戏", "图片识别", "符号与实物配对"]
                            },
                            {
                                title: "计划能力",
                                description: "开始表现出简单的计划能力",
                                suggestions: ["计划性活动", "目标设定游戏", "步骤分解练习"]
                            },
                            {
                                title: "逻辑思维萌芽",
                                description: "逻辑思维开始萌芽，能够进行简单推理",
                                suggestions: ["逻辑游戏", "因果推理", "简单推理练习"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "运动技能精进",
                                description: "各项运动技能进一步精进",
                                suggestions: ["运动技能练习", "体能游戏", "协调性训练"]
                            },
                            {
                                title: "精细动作控制",
                                description: "精细动作控制能力显著提高",
                                suggestions: ["精细动作挑战", "手工制作", "绘画涂鸦"]
                            },
                            {
                                title: "身体意识",
                                description: "对自己身体的意识和控制增强",
                                suggestions: ["身体部位认知", "身体控制游戏", "自我意识培养"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇量扩大",
                                description: "词汇量继续快速扩大，可能达到20-50个词",
                                suggestions: ["持续词汇输入", "词汇分类学习", "情境化使用"]
                            },
                            {
                                title: "句子结构",
                                description: "开始使用简单的句子结构",
                                suggestions: ["句子结构示范", "语法规则教学", "句式练习"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "情绪调节",
                                description: "情绪调节能力有所提高",
                                suggestions: ["情绪识别教学", "调节策略教授", "情绪表达支持"]
                            },
                            {
                                title: "社交互动复杂",
                                description: "社交互动变得更加复杂和有意义",
                                suggestions: ["复杂社交游戏", "角色扮演", "社交技能练习"]
                            }
                        ]
                    }
                ]
            },
            18: {
                title: "18个月宝宝发展评估",
                ageInfo: "幼儿期中期 - 自我意识发展",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "自我认知",
                                description: "开始发展自我认知，能够在镜子中认出自己",
                                suggestions: ["镜子游戏", "自我认知活动", "身份意识培养"]
                            },
                            {
                                title: "时间概念初步",
                                description: "开始理解简单的时间概念，如\"现在\"\"等一下\"",
                                suggestions: ["时间概念教学", "日常时间安排", "时间词汇使用"]
                            },
                            {
                                title: "想象力发展",
                                description: "想象力显著发展，喜欢假想游戏",
                                suggestions: ["想象力游戏", "角色扮演", "创造性活动"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "跳跃尝试",
                                description: "开始尝试跳跃动作，虽然还不够协调",
                                suggestions: ["跳跃练习", "蹦跳游戏", "平衡训练"]
                            },
                            {
                                title: "楼梯攀爬",
                                description: "能够在帮助下上下楼梯",
                                suggestions: ["楼梯练习", "安全攀爬", "平衡协调训练"]
                            },
                            {
                                title: "工具使用熟练",
                                description: "工具使用变得更加熟练和有目的",
                                suggestions: ["工具使用练习", "日常工具体验", "创新使用鼓励"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "语言爆发",
                                description: "语言发展进入爆发期，词汇量快速增长",
                                suggestions: ["丰富语言环境", "词汇扩展游戏", "语言使用鼓励"]
                            },
                            {
                                title: "疑问词使用",
                                description: "开始使用简单的疑问词，如\"什么\"\"哪里\"",
                                suggestions: ["疑问词教学", "问答游戏", "好奇心培养"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "自主性强化",
                                description: "自主性进一步强化，坚持自己的想法",
                                suggestions: ["尊重自主性", "提供选择机会", "合理界限设定"]
                            },
                            {
                                title: "情感表达丰富",
                                description: "情感表达更加丰富和细腻",
                                suggestions: ["情感词汇教学", "情感表达支持", "情感交流深化"]
                            }
                        ]
                    }
                ]
            },
            19: {
                title: "19个月宝宝发展评估",
                ageInfo: "语言快速期 - 社交技能提升",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "复杂问题解决",
                                description: "能够解决更复杂的问题，使用多步骤策略",
                                suggestions: ["复杂拼图", "多步骤任务", "策略性游戏"]
                            },
                            {
                                title: "分类精细化",
                                description: "分类能力更加精细，能按多个特征分类",
                                suggestions: ["多维度分类", "复杂分类游戏", "属性识别"]
                            },
                            {
                                title: "记忆策略使用",
                                description: "开始主动使用记忆策略",
                                suggestions: ["记忆技巧教学", "重复练习", "记忆游戏"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "跑步熟练",
                                description: "跑步动作熟练，能够改变方向",
                                suggestions: ["方向变换练习", "追逐游戏", "敏捷性训练"]
                            },
                            {
                                title: "精细动作精确",
                                description: "精细动作更加精确，能完成复杂手工",
                                suggestions: ["复杂手工制作", "精细操作练习", "创意活动"]
                            },
                            {
                                title: "身体协调优化",
                                description: "整体身体协调性进一步优化",
                                suggestions: ["协调性游戏", "舞蹈活动", "运动技能练习"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇量激增",
                                description: "词汇量激增，可能达到50-100个词",
                                suggestions: ["词汇扩展活动", "分类词汇学习", "情境化使用"]
                            },
                            {
                                title: "简单句子",
                                description: "能够说出简单但完整的句子",
                                suggestions: ["句子结构练习", "语法示范", "表达鼓励"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "合作游戏",
                                description: "开始参与简单的合作游戏",
                                suggestions: ["合作性游戏", "团队活动", "分享练习"]
                            },
                            {
                                title: "情感理解深化",
                                description: "对他人情感的理解更加深入",
                                suggestions: ["情感识别游戏", "同理心培养", "情感交流"]
                            }
                        ]
                    }
                ]
            },
            20: {
                title: "20个月宝宝发展评估",
                ageInfo: "创造力发展期 - 独立性强化",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "抽象思维发展",
                                description: "抽象思维能力开始发展",
                                suggestions: ["抽象概念游戏", "概念教学", "思维训练"]
                            },
                            {
                                title: "序列推理",
                                description: "能够进行简单的序列推理",
                                suggestions: ["序列游戏", "模式识别", "逻辑推理练习"]
                            },
                            {
                                title: "创造性解决方案",
                                description: "能够想出创造性的解决方案",
                                suggestions: ["开放性问题", "创意挑战", "多元解决方案"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "跳跃协调",
                                description: "跳跃动作更加协调，能够双脚跳",
                                suggestions: ["跳跃练习", "蹦床活动", "节奏跳跃"]
                            },
                            {
                                title: "球类技能",
                                description: "开始发展球类运动技能",
                                suggestions: ["球类游戏", "投掷练习", "接球训练"]
                            },
                            {
                                title: "精细动作创新",
                                description: "精细动作中表现出创新性",
                                suggestions: ["创意手工", "艺术活动", "自由创作"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "语法规则掌握",
                                description: "开始掌握基本的语法规则",
                                suggestions: ["语法练习", "句式变化", "语言游戏"]
                            },
                            {
                                title: "交流策略",
                                description: "使用更多样的交流策略",
                                suggestions: ["交流技巧教学", "表达方式多样化", "沟通练习"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "独立性坚持",
                                description: "坚持独立性，有强烈的自主意愿",
                                suggestions: ["支持独立尝试", "适当引导", "安全界限"]
                            },
                            {
                                title: "社交规则遵守",
                                description: "开始理解并遵守简单的社交规则",
                                suggestions: ["社交规则教学", "行为示范", "规则练习"]
                            }
                        ]
                    }
                ]
            },
            21: {
                title: "21个月宝宝发展评估",
                ageInfo: "语言表达期 - 社交能力提升",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "符号操作",
                                description: "能够操作和理解更复杂的符号系统",
                                suggestions: ["符号游戏", "图标识别", "符号配对"]
                            },
                            {
                                title: "因果推理强化",
                                description: "因果推理能力显著强化",
                                suggestions: ["因果实验", "推理游戏", "逻辑链条"]
                            },
                            {
                                title: "计划执行",
                                description: "能够制定并执行简单计划",
                                suggestions: ["计划性活动", "目标导向任务", "步骤执行"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "复杂运动序列",
                                description: "能够完成复杂的运动序列",
                                suggestions: ["动作序列练习", "舞蹈动作", "运动组合"]
                            },
                            {
                                title: "平衡技能",
                                description: "平衡技能显著提高",
                                suggestions: ["平衡游戏", "单脚站立", "平衡木练习"]
                            },
                            {
                                title: "工具创新使用",
                                description: "创新性地使用各种工具",
                                suggestions: ["工具创新游戏", "多功能使用", "创意工具"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "复杂句式",
                                description: "开始使用更复杂的句式结构",
                                suggestions: ["复杂句式示范", "语法扩展", "句式练习"]
                            },
                            {
                                title: "叙述能力",
                                description: "开始发展简单的叙述能力",
                                suggestions: ["故事叙述", "经历分享", "叙述练习"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "同伴互动",
                                description: "与同伴的互动更加复杂和有意义",
                                suggestions: ["同伴游戏", "社交技能练习", "互动引导"]
                            },
                            {
                                title: "情绪调节策略",
                                description: "开始学习使用情绪调节策略",
                                suggestions: ["情绪管理教学", "调节技巧", "情绪表达"]
                            }
                        ]
                    }
                ]
            },
            22: {
                title: "22个月宝宝发展评估",
                ageInfo: "思维发展期 - 自我控制萌芽",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "概念层次化",
                                description: "理解概念的层次关系，如动物-狗-小狗",
                                suggestions: ["层次概念游戏", "分类层次", "概念关系"]
                            },
                            {
                                title: "时间序列理解",
                                description: "对时间序列的理解更加深入",
                                suggestions: ["时间序列游戏", "事件顺序", "时间概念"]
                            },
                            {
                                title: "假设思维",
                                description: "开始进行简单的假设思维",
                                suggestions: ["假设游戏", "\"如果...那么\"", "想象情境"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "运动技能整合",
                                description: "各项运动技能开始整合",
                                suggestions: ["综合运动游戏", "技能组合", "运动协调"]
                            },
                            {
                                title: "精细动作艺术",
                                description: "精细动作中表现出艺术性",
                                suggestions: ["艺术创作", "精细艺术", "创意表达"]
                            },
                            {
                                title: "身体表达",
                                description: "用身体动作进行表达和交流",
                                suggestions: ["身体语言", "动作表达", "肢体交流"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "词汇分类使用",
                                description: "能够按类别使用词汇",
                                suggestions: ["词汇分类游戏", "语义网络", "词汇关系"]
                            },
                            {
                                title: "语言创造",
                                description: "开始创造新的词汇或表达方式",
                                suggestions: ["语言创造游戏", "新词鼓励", "创意表达"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "自我控制萌芽",
                                description: "自我控制能力开始萌芽",
                                suggestions: ["自控练习", "延迟满足", "行为管理"]
                            },
                            {
                                title: "道德感初步",
                                description: "开始发展初步的道德感",
                                suggestions: ["道德故事", "是非判断", "价值观培养"]
                            }
                        ]
                    }
                ]
            },
            23: {
                title: "23个月宝宝发展评估",
                ageInfo: "语言成熟期 - 社交复杂化",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "抽象概念掌握",
                                description: "能够掌握更多抽象概念，如颜色、形状、数量",
                                suggestions: ["抽象概念游戏", "概念应用", "抽象思维训练"]
                            },
                            {
                                title: "问题解决创新",
                                description: "问题解决中表现出更多创新性",
                                suggestions: ["创新挑战", "多元解决方案", "创意思维"]
                            },
                            {
                                title: "学习策略",
                                description: "开始使用不同的学习策略",
                                suggestions: ["学习方法教学", "策略选择", "学习效果观察"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "运动技能成熟",
                                description: "各项运动技能趋于成熟",
                                suggestions: ["技能巩固练习", "运动挑战", "技能展示"]
                            },
                            {
                                title: "复杂协调动作",
                                description: "能够完成复杂的协调动作",
                                suggestions: ["协调性挑战", "复杂动作序列", "技能组合"]
                            },
                            {
                                title: "运动创造性",
                                description: "在运动中表现出创造性",
                                suggestions: ["创意运动", "自由表达", "运动艺术"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "语言表达成熟",
                                description: "语言表达能力趋于成熟，词汇量可达100-200词",
                                suggestions: ["语言表达练习", "词汇扩展", "表达技巧"]
                            },
                            {
                                title: "对话技能",
                                description: "对话技能显著提高，能够进行简单对话",
                                suggestions: ["对话练习", "交流技巧", "倾听技能"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "复杂社交互动",
                                description: "能够进行更复杂的社交互动",
                                suggestions: ["复杂社交游戏", "角色扮演", "社交技能"]
                            },
                            {
                                title: "情感表达精确",
                                description: "情感表达更加精确和细腻",
                                suggestions: ["情感词汇丰富", "情感表达练习", "情感交流"]
                            }
                        ]
                    }
                ]
            },
            24: {
                title: "24个月宝宝发展评估",
                ageInfo: "两岁里程碑 - 全面发展期",
                sections: [
                    {
                        title: "🧠 认知发展",
                        icon: "🧠",
                        items: [
                            {
                                title: "逻辑思维建立",
                                description: "逻辑思维能力基本建立，能进行简单推理",
                                suggestions: ["逻辑游戏", "推理练习", "思维训练"]
                            },
                            {
                                title: "记忆系统成熟",
                                description: "记忆系统更加成熟，能记住复杂信息",
                                suggestions: ["记忆挑战", "信息整合", "记忆策略"]
                            },
                            {
                                title: "学习能力综合",
                                description: "各项学习能力开始综合发展",
                                suggestions: ["综合学习活动", "多元智能", "全面发展"]
                            }
                        ]
                    },
                    {
                        title: "🤲 运动发展",
                        icon: "🤲",
                        items: [
                            {
                                title: "运动技能完善",
                                description: "基本运动技能基本完善",
                                suggestions: ["技能完善练习", "运动挑战", "技能展示"]
                            },
                            {
                                title: "精细动作精通",
                                description: "精细动作技能趋于精通",
                                suggestions: ["精细技能挑战", "艺术创作", "技能应用"]
                            },
                            {
                                title: "身体控制成熟",
                                description: "对身体的控制能力基本成熟",
                                suggestions: ["身体控制游戏", "协调训练", "技能整合"]
                            }
                        ]
                    },
                    {
                        title: "🗣️ 语言发展",
                        icon: "🗣️",
                        items: [
                            {
                                title: "语言系统建立",
                                description: "基本语言系统建立，能进行有效交流",
                                suggestions: ["语言系统巩固", "交流练习", "表达技巧"]
                            },
                            {
                                title: "语法规则掌握",
                                description: "基本语法规则基本掌握",
                                suggestions: ["语法练习", "句式变化", "语言游戏"]
                            }
                        ]
                    },
                    {
                        title: "😊 社交情感",
                        icon: "😊",
                        items: [
                            {
                                title: "社交技能成熟",
                                description: "基本社交技能趋于成熟",
                                suggestions: ["社交技能巩固", "人际交往", "社交实践"]
                            },
                            {
                                title: "情感调节能力",
                                description: "情感调节能力显著提高",
                                suggestions: ["情感管理", "调节策略", "情感表达"]
                            },
                            {
                                title: "自我意识完善",
                                description: "自我意识进一步完善",
                                suggestions: ["自我认知", "身份意识", "个性发展"]
                            }
                        ]
                    }
                ]
            }
        };

        // 当前选中的月份
        let currentMonth = 1;

        // 评估结果存储
        let assessmentResults = JSON.parse(localStorage.getItem('babyAssessment')) || {};

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            generateMonthNavigation();
            showMonthContent(1);
            updateProgress();
            initializeSwipeGestures();
        });

        // 初始化滑动手势
        function initializeSwipeGestures() {
            let startX = 0;
            let startY = 0;

            document.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            document.addEventListener('touchend', function(e) {
                if (!startX || !startY) return;

                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;

                const diffX = startX - endX;
                const diffY = startY - endY;

                // 确保是水平滑动且滑动距离足够
                if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                    if (diffX > 0 && currentMonth < 24) {
                        // 向左滑动，下一个月
                        showMonthContent(currentMonth + 1);
                    } else if (diffX < 0 && currentMonth > 1) {
                        // 向右滑动，上一个月
                        showMonthContent(currentMonth - 1);
                    }
                }

                startX = 0;
                startY = 0;
            });

            // 添加键盘导航支持
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft' && currentMonth > 1) {
                    showMonthContent(currentMonth - 1);
                } else if (e.key === 'ArrowRight' && currentMonth < 24) {
                    showMonthContent(currentMonth + 1);
                }
            });
        }

        // 生成月份导航
        function generateMonthNavigation() {
            const monthNav = document.getElementById('monthNav');
            for (let i = 1; i <= 24; i++) {
                const button = document.createElement('button');
                button.className = 'month-btn';
                button.textContent = `${i}月`;
                button.onclick = () => showMonthContent(i);
                if (i === 1) button.classList.add('active');
                monthNav.appendChild(button);
            }
        }

        // 显示月份内容
        function showMonthContent(month) {
            currentMonth = month;

            // 更新导航按钮状态
            document.querySelectorAll('.month-btn').forEach((btn, index) => {
                btn.classList.toggle('active', index + 1 === month);
            });

            // 生成月份内容
            const monthContent = document.getElementById('monthContent');
            const data = assessmentData[month];

            if (!data) {
                monthContent.innerHTML = `
                    <div class="month-header">
                        <h2>${month}个月宝宝发展评估</h2>
                        <p class="age-info">评估内容正在完善中...</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="month-content active">
                    <div class="month-header">
                        <h2>${data.title}</h2>
                        <p class="age-info">${data.ageInfo}</p>
                    </div>
            `;

            data.sections.forEach(section => {
                html += `
                    <div class="assessment-section">
                        <div class="section-title">
                            <span>${section.icon}</span>
                            <span>${section.title}</span>
                        </div>
                        <div class="assessment-items">
                `;

                section.items.forEach((item, itemIndex) => {
                    const itemId = `${month}-${section.title}-${itemIndex}`;
                    const result = assessmentResults[itemId] || '';

                    html += `
                        <div class="assessment-item">
                            <div class="item-header">
                                <div class="item-title">${item.title}</div>
                                <div class="assessment-checkbox">
                                    <div class="checkbox-option achieved">
                                        <input type="radio" name="${itemId}" value="achieved" id="${itemId}-achieved" ${result === 'achieved' ? 'checked' : ''} onchange="updateAssessment('${itemId}', 'achieved')">
                                        <label for="${itemId}-achieved">✓</label>
                                    </div>
                                    <div class="checkbox-option partial">
                                        <input type="radio" name="${itemId}" value="partial" id="${itemId}-partial" ${result === 'partial' ? 'checked' : ''} onchange="updateAssessment('${itemId}', 'partial')">
                                        <label for="${itemId}-partial">~</label>
                                    </div>
                                    <div class="checkbox-option not-achieved">
                                        <input type="radio" name="${itemId}" value="not-achieved" id="${itemId}-not-achieved" ${result === 'not-achieved' ? 'checked' : ''} onchange="updateAssessment('${itemId}', 'not-achieved')">
                                        <label for="${itemId}-not-achieved">✗</label>
                                    </div>
                                </div>
                            </div>
                            <div class="item-description">${item.description}</div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            monthContent.innerHTML = html;

            updateProgress();
            generateSuggestions();
        }

        // 更新评估结果
        function updateAssessment(itemId, value) {
            assessmentResults[itemId] = value;
            localStorage.setItem('babyAssessment', JSON.stringify(assessmentResults));
            updateProgress();
            generateSuggestions();
        }

        // 更新进度
        function updateProgress() {
            const data = assessmentData[currentMonth];
            if (!data) return;

            let totalItems = 0;
            let completedItems = 0;

            data.sections.forEach(section => {
                section.items.forEach((item, itemIndex) => {
                    totalItems++;
                    const itemId = `${currentMonth}-${section.title}-${itemIndex}`;
                    if (assessmentResults[itemId]) {
                        completedItems++;
                    }
                });
            });

            const percentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

            document.getElementById('progressFill').style.width = `${percentage}%`;
            document.getElementById('progressText').textContent =
                `已完成 ${completedItems}/${totalItems} 项评估 (${percentage}%)`;
        }

        // 生成个性化建议
        function generateSuggestions() {
            const data = assessmentData[currentMonth];
            if (!data) return;

            const suggestions = [];
            const needsAttention = [];

            data.sections.forEach(section => {
                section.items.forEach((item, itemIndex) => {
                    const itemId = `${currentMonth}-${section.title}-${itemIndex}`;
                    const result = assessmentResults[itemId];

                    if (result === 'not-achieved' || result === 'partial') {
                        needsAttention.push({
                            title: item.title,
                            suggestions: item.suggestions,
                            level: result === 'not-achieved' ? 'high' : 'medium'
                        });
                    }
                });
            });

            const suggestionsDiv = document.getElementById('suggestions');
            const suggestionContent = document.getElementById('suggestionContent');

            if (needsAttention.length > 0) {
                suggestionsDiv.style.display = 'block';
                let html = '';

                needsAttention.forEach(item => {
                    const priority = item.level === 'high' ? '🔴 重点关注' : '🟡 需要练习';
                    html += `
                        <div class="suggestion-item">
                            <strong>${priority}: ${item.title}</strong>
                            <ul style="margin-top: 0.5rem; padding-left: 1rem;">
                                ${item.suggestions.map(s => `<li>${s}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                });

                suggestionContent.innerHTML = html;
            } else {
                suggestionsDiv.style.display = 'none';
            }
        }

        // 生成评估报告
        function generateReport() {
            const reportSummary = document.getElementById('reportSummary');
            const summaryGrid = document.getElementById('summaryGrid');

            let totalAssessed = 0;
            let totalAchieved = 0;
            let totalPartial = 0;
            let totalNotAchieved = 0;
            let monthsWithData = 0;

            // 统计所有月份的评估数据
            for (let month = 1; month <= 24; month++) {
                const data = assessmentData[month];
                if (!data) continue;

                let monthAssessed = 0;
                let monthTotal = 0;

                data.sections.forEach(section => {
                    section.items.forEach((item, itemIndex) => {
                        monthTotal++;
                        const itemId = `${month}-${section.title}-${itemIndex}`;
                        const result = assessmentResults[itemId];

                        if (result) {
                            monthAssessed++;
                            totalAssessed++;

                            if (result === 'achieved') totalAchieved++;
                            else if (result === 'partial') totalPartial++;
                            else if (result === 'not-achieved') totalNotAchieved++;
                        }
                    });
                });

                if (monthAssessed > 0) monthsWithData++;
            }

            const achievementRate = totalAssessed > 0 ? Math.round((totalAchieved / totalAssessed) * 100) : 0;

            summaryGrid.innerHTML = `
                <div class="summary-item">
                    <div class="summary-number">${monthsWithData}</div>
                    <div class="summary-label">已评估月份</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${totalAssessed}</div>
                    <div class="summary-label">总评估项目</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${totalAchieved}</div>
                    <div class="summary-label">已达到</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${totalPartial}</div>
                    <div class="summary-label">部分达到</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${totalNotAchieved}</div>
                    <div class="summary-label">需要关注</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${achievementRate}%</div>
                    <div class="summary-label">达成率</div>
                </div>
            `;

            reportSummary.style.display = 'block';
            reportSummary.scrollIntoView({ behavior: 'smooth' });
        }

        // 导出数据
        function exportData() {
            const exportData = {
                timestamp: new Date().toISOString(),
                assessmentResults: assessmentResults,
                summary: generateSummaryData()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `baby-assessment-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 生成摘要数据
        function generateSummaryData() {
            const summary = {};

            for (let month = 1; month <= 24; month++) {
                const data = assessmentData[month];
                if (!data) continue;

                summary[month] = {
                    title: data.title,
                    assessed: 0,
                    achieved: 0,
                    partial: 0,
                    notAchieved: 0
                };

                data.sections.forEach(section => {
                    section.items.forEach((item, itemIndex) => {
                        const itemId = `${month}-${section.title}-${itemIndex}`;
                        const result = assessmentResults[itemId];

                        if (result) {
                            summary[month].assessed++;
                            if (result === 'achieved') summary[month].achieved++;
                            else if (result === 'partial') summary[month].partial++;
                            else if (result === 'not-achieved') summary[month].notAchieved++;
                        }
                    });
                });
            }

            return summary;
        }

        // 重置数据
        function clearData() {
            if (confirm('确定要清除所有评估数据吗？此操作不可恢复。')) {
                localStorage.removeItem('babyAssessment');
                assessmentResults = {};
                showMonthContent(currentMonth);
                document.getElementById('reportSummary').style.display = 'none';
                alert('评估数据已清除');
            }
        }
    </script>
</body>
</html>
