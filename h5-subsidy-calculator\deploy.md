# EdgeOne Pages 部署指南

## 项目信息
- 项目名称: 育儿补贴计算器
- 项目类型: 静态H5应用
- 框架: 原生HTML/CSS/JavaScript

## 部署步骤

### 方法一：通过EdgeOne控制台部署

1. 登录腾讯云EdgeOne控制台
2. 进入 Pages 服务
3. 点击"新建站点"
4. 选择"上传文件"方式
5. 上传以下文件：
   - index.html
   - styles.css
   - script.js
   - manifest.json
   - sw.js
   - test.html (可选)

### 方法二：通过Git仓库部署

1. 将代码推送到Git仓库（GitHub/GitLab等）
2. 在EdgeOne Pages中选择"Git仓库"
3. 连接您的仓库
4. 配置构建设置：
   - 构建命令: 无需构建命令
   - 输出目录: ./
   - 安装命令: 无需安装命令

### 方法三：通过CLI部署

```bash
# 安装EdgeOne CLI（如果有的话）
npm install -g @edgeone/cli

# 登录
edgeone login

# 部署
edgeone deploy
```

## 配置说明

### 环境变量
无需特殊环境变量

### 域名配置
- 支持自定义域名
- 自动HTTPS
- CDN加速

### 缓存策略
- HTML文件: 不缓存
- 静态资源(CSS/JS): 长期缓存
- Service Worker: 不缓存

## 注意事项

1. 确保所有文件路径使用相对路径
2. Service Worker需要HTTPS环境才能正常工作
3. 建议配置自定义域名以获得更好的用户体验
4. 定期检查和更新缓存策略

## 验证部署

部署完成后，访问分配的域名，检查：
- [ ] 页面正常加载
- [ ] 样式正确显示
- [ ] JavaScript功能正常
- [ ] PWA功能可用
- [ ] 移动端适配良好

## 故障排除

如果遇到问题：
1. 检查控制台错误信息
2. 验证文件路径是否正确
3. 确认HTTPS配置
4. 检查缓存设置
