<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>婴儿认知发展月度评估</title>
<style>
*{margin:0;padding:0;box-sizing:border-box}
body{font-family:'PingFang SC','Hiragino Sans GB','Microsoft YaHei',sans-serif;line-height:1.6;color:#333;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh}
.container{max-width:900px;margin:0 auto;background:white;min-height:100vh;box-shadow:0 0 20px rgba(0,0,0,0.1)}
.header{background:linear-gradient(135deg,#ff9a9e 0%,#fecfef 50%,#fecfef 100%);padding:2rem 1rem;text-align:center;color:white}
.header h1{font-size:1.8rem;margin-bottom:0.5rem;text-shadow:0 2px 4px rgba(0,0,0,0.3)}
.subtitle{font-size:1rem;opacity:0.9}
.month-selector{background:#f8f9fa;padding:1rem;border-bottom:2px solid #e9ecef;position:sticky;top:0;z-index:100}
.month-nav{display:flex;overflow-x:auto;gap:0.5rem;padding:0.5rem 0}
.month-btn{min-width:60px;padding:0.8rem 1rem;border:none;background:white;border-radius:25px;font-size:0.9rem;font-weight:500;color:#6c757d;cursor:pointer;transition:all 0.3s ease;white-space:nowrap;box-shadow:0 2px 4px rgba(0,0,0,0.1)}
.month-btn:hover{background:#e9ecef;color:#495057;transform:translateY(-2px)}
.month-btn.active{background:linear-gradient(45deg,#007bff,#0056b3);color:white;transform:translateY(-2px);box-shadow:0 4px 8px rgba(0,123,255,0.3)}
.content{padding:1.5rem}
.month-content{display:none;animation:fadeIn 0.5s ease-in}
.month-content.active{display:block}
@keyframes fadeIn{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}
.month-header{text-align:center;margin-bottom:2rem;padding:1.5rem;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:15px;color:white}
.month-header h2{font-size:1.6rem;margin-bottom:0.5rem}
.month-header .age-info{font-size:1rem;opacity:0.9}
.assessment-section{margin-bottom:2rem}
.section-title{display:flex;align-items:center;gap:0.5rem;font-size:1.2rem;font-weight:600;color:#2c3e50;margin-bottom:1rem;padding-bottom:0.5rem;border-bottom:2px solid #f8f9fa}
.assessment-items{display:grid;gap:1rem}
.assessment-item{background:white;border-radius:12px;padding:1.5rem;box-shadow:0 4px 6px rgba(0,0,0,0.1);border-left:4px solid #007bff;transition:all 0.3s ease}
.assessment-item:hover{transform:translateY(-2px);box-shadow:0 8px 15px rgba(0,0,0,0.15)}
.item-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:1rem}
.item-title{font-weight:600;color:#2c3e50;flex:1}
.assessment-checkbox{display:flex;gap:0.5rem;flex-shrink:0}
.checkbox-option{position:relative}
.checkbox-option input[type="radio"]{display:none}
.checkbox-option label{display:flex;align-items:center;justify-content:center;width:35px;height:35px;border-radius:50%;cursor:pointer;transition:all 0.3s ease;font-size:0.8rem;font-weight:500}
.checkbox-option.achieved label{background:#e8f5e8;color:#28a745;border:2px solid #28a745}
.checkbox-option.partial label{background:#fff3cd;color:#ffc107;border:2px solid #ffc107}
.checkbox-option.not-achieved label{background:#f8d7da;color:#dc3545;border:2px solid #dc3545}
.checkbox-option input[type="radio"]:checked + label{transform:scale(1.1);box-shadow:0 4px 8px rgba(0,0,0,0.2)}
.item-description{color:#666;font-size:0.9rem;line-height:1.5}
.progress-summary{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:15px;padding:1.5rem;color:white;margin-bottom:2rem;text-align:center}
.progress-bar{background:rgba(255,255,255,0.3);border-radius:10px;height:20px;margin:1rem 0;overflow:hidden}
.progress-fill{background:linear-gradient(45deg,#28a745,#20c997);height:100%;border-radius:10px;transition:width 0.5s ease}
.suggestions{background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-top:2rem}
.suggestions h4{color:#2c3e50;margin-bottom:1rem}
.suggestion-item{background:white;border-radius:8px;padding:1rem;margin-bottom:0.5rem;border-left:4px solid #17a2b8}
.action-buttons{display:flex;gap:1rem;margin:2rem 0;flex-wrap:wrap}
.action-btn{padding:0.8rem 1.5rem;border:none;border-radius:25px;font-weight:500;cursor:pointer;transition:all 0.3s ease;text-decoration:none;display:inline-flex;align-items:center;gap:0.5rem}
.btn-primary{background:linear-gradient(45deg,#007bff,#0056b3);color:white}
.btn-secondary{background:linear-gradient(45deg,#6c757d,#495057);color:white}
.btn-success{background:linear-gradient(45deg,#28a745,#1e7e34);color:white}
.action-btn:hover{transform:translateY(-2px);box-shadow:0 4px 8px rgba(0,0,0,0.2)}
.report-summary{background:#f8f9fa;border-radius:12px;padding:1.5rem;margin:2rem 0;border:1px solid #e9ecef}
.summary-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;margin-top:1rem}
.summary-item{text-align:center;padding:1rem;background:white;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1)}
.summary-number{font-size:2rem;font-weight:bold;color:#007bff}
.summary-label{color:#6c757d;font-size:0.9rem}
@media (max-width:768px){
.container{margin:0;border-radius:0}
.header{padding:1.5rem 1rem}
.header h1{font-size:1.5rem}
.month-nav{justify-content:flex-start;padding-bottom:1rem}
.month-btn{min-width:50px;padding:0.6rem 0.8rem;font-size:0.8rem}
.content{padding:1rem}
.assessment-item{padding:1rem}
.item-header{flex-direction:column;gap:1rem;align-items:center;text-align:center}
.assessment-checkbox{justify-content:center}
.action-buttons{flex-direction:column;align-items:center}
.action-btn{width:100%;max-width:300px;justify-content:center}
.summary-grid{grid-template-columns:repeat(2,1fr);gap:0.5rem}
.month-header h2{font-size:1.3rem}
.section-title{font-size:1.1rem;text-align:center;justify-content:center}
}
@media (max-width:480px){
.header h1{font-size:1.3rem}
.month-btn{min-width:45px;padding:0.5rem 0.6rem;font-size:0.75rem}
.content{padding:0.5rem}
.month-header{padding:1rem;margin-bottom:1rem}
.assessment-item{padding:0.8rem;margin-bottom:0.8rem}
.checkbox-option label{width:30px;height:30px;font-size:0.7rem}
.summary-grid{grid-template-columns:1fr}
.summary-item{padding:0.8rem}
.summary-number{font-size:1.5rem}
}
</style>
</head>
<body>
<div class="container">
<header class="header">
<h1>👶 婴儿认知发展月度评估</h1>
<p class="subtitle">科学评估宝宝每个月的发展里程碑</p>
</header>
<div class="month-selector">
<div class="month-nav" id="monthNav"></div>
</div>
<main class="content">
<div class="progress-summary">
<h3>📊 本月发展评估进度</h3>
<div class="progress-bar">
<div class="progress-fill" id="progressFill" style="width: 0%"></div>
</div>
<p id="progressText">请开始评估宝宝的发展情况</p>
</div>
<div id="monthContent"></div>
<div class="action-buttons">
<button class="action-btn btn-primary" onclick="generateReport()">📊 生成评估报告</button>
<button class="action-btn btn-secondary" onclick="exportData()">💾 导出数据</button>
<button class="action-btn btn-success" onclick="clearData()">🔄 重置评估</button>
</div>
<div class="report-summary" id="reportSummary" style="display: none;">
<h4>📈 评估总结报告</h4>
<div class="summary-grid" id="summaryGrid"></div>
</div>
<div class="suggestions" id="suggestions" style="display: none;">
<h4>💡 个性化建议</h4>
<div id="suggestionContent"></div>
</div>
</main>
</div>
<script>
const assessmentData = {
1: {
title: "1个月宝宝发展评估",
ageInfo: "新生儿期 - 适应期",
sections: [
{
title: "🧠 认知发展",
icon: "🧠",
items: [
{
title: "视觉注意力",
description: "能够短暂注视人脸或高对比度物体（20-30cm距离）",
suggestions: ["使用黑白对比强烈的图片", "保持眼神交流", "缓慢移动物体让宝宝追视"]
},
{
title: "听觉反应",
description: "对突然的声音有惊跳反应，喜欢熟悉的声音",
suggestions: ["轻柔地与宝宝说话", "播放轻柔音乐", "模仿宝宝的声音"]
},
{
title: "触觉感知",
description: "对触摸有反应，喜欢温暖柔软的接触",
suggestions: ["温柔的抚触按摩", "不同质地的布料接触", "皮肤接触时间"]
}
]
},
{
title: "🤲 运动发展",
icon: "🤲",
items: [
{
title: "原始反射",
description: "具有完整的原始反射（吸吮、握持、惊跳反射等）",
suggestions: ["观察反射是否正常", "轻触手心观察握持反射", "注意反射的对称性"]
},
{
title: "头部控制",
description: "俯卧时能短暂抬头（几秒钟）",
suggestions: ["每天进行俯卧练习", "用玩具吸引抬头", "逐渐增加俯卧时间"]
},
{
title: "肌肉张力",
description: "四肢肌肉张力正常，无过度紧张或松弛",
suggestions: ["观察宝宝的姿势", "轻柔的被动运动", "注意肌肉张力变化"]
}
]
},
{
title: "🗣️ 语言发展",
icon: "🗣️",
items: [
{
title: "发声反应",
description: "会发出简单的声音，如"啊"、"呜"等",
suggestions: ["回应宝宝的声音", "模仿宝宝发音", "用不同音调说话"]
},
{
title: "哭声分化",
description: "不同需求时哭声有所不同（饿、困、不适）",
suggestions: ["学习识别不同哭声", "及时回应宝宝需求", "观察哭声模式"]
}
]
},
{
title: "😊 社交情感",
icon: "😊",
items: [
{
title: "注视人脸",
description: "喜欢看人的脸，特别是妈妈的脸",
suggestions: ["多进行面对面交流", "保持眼神接触", "做夸张的面部表情"]
},
{
title: "安抚反应",
description: "听到熟悉声音或被抱起时能够安静下来",
suggestions: ["建立规律的安抚方式", "使用一致的声音", "温柔的摇摆动作"]
}
]
}
]
},
2: {
title: "2个月宝宝发展评估",
ageInfo: "快速发展期 - 社交萌芽",
sections: [
{
title: "🧠 认知发展",
icon: "🧠",
items: [
{
title: "视觉追踪",
description: "能够追踪缓慢移动的物体，视线可跟随180度",
suggestions: ["用彩色玩具练习追视", "左右移动物体", "增加追视距离"]
},
{
title: "注意力持续",
description: "能够持续注视感兴趣的物体30秒以上",
suggestions: ["提供有趣的视觉刺激", "观察宝宝的兴趣点", "避免过度刺激"]
},
{
title: "记忆萌芽",
description: "开始对熟悉的人和环境表现出认知",
suggestions: ["保持环境的一致性", "重复日常活动", "建立固定的作息"]
}
]
},
{
title: "🤲 运动发展",
icon: "🤲",
items: [
{
title: "头部控制增强",
description: "俯卧时能抬头45度，持续时间更长",
suggestions: ["增加俯卧时间", "用玩具鼓励抬头", "支撑练习"]
},
{
title: "手部动作",
description: "握持反射开始减弱，手指开始放松",
suggestions: ["轻抚手心", "提供不同触感物品", "观察手部变化"]
},
{
title: "腿部活动",
description: "仰卧时腿部活动更加协调有力",
suggestions: ["鼓励踢腿运动", "轻柔的腿部按摩", "提供踢踏玩具"]
}
]
},
{
title: "🗣️ 语言发展",
icon: "🗣️",
items: [
{
title: "咕咕声",
description: "开始发出咕咕声、咯咯声等愉快的声音",
suggestions: ["回应宝宝的声音", "模仿发音", "创造对话氛围"]
},
{
title: "声音定位",
description: "听到声音时会转向声源方向",
suggestions: ["从不同方向发出声音", "使用摇铃等玩具", "观察反应方向"]
}
]
},
{
title: "😊 社交情感",
icon: "😊",
items: [
{
title: "社交性微笑",
description: "开始出现真正的社交性微笑，不只是生理性微笑",
suggestions: ["多与宝宝互动", "做夸张表情", "及时回应微笑"]
},
{
title: "情绪表达",
description: "开始表现出愉快、不满等基本情绪",
suggestions: ["观察情绪变化", "及时回应情绪", "创造愉快氛围"]
}
]
}
]
}
};

let currentMonth = 1;
let assessmentResults = JSON.parse(localStorage.getItem('assessmentResults') || '{}');

function initializeApp() {
    generateMonthNavigation();
    showMonth(1);
    updateProgress();
}

function generateMonthNavigation() {
    const monthNav = document.getElementById('monthNav');
    monthNav.innerHTML = '';

    for (let i = 1; i <= 24; i++) {
        const btn = document.createElement('button');
        btn.className = 'month-btn';
        btn.textContent = i + '月';
        btn.onclick = () => showMonth(i);
        if (i === currentMonth) btn.classList.add('active');
        monthNav.appendChild(btn);
    }
}

function showMonth(month) {
    currentMonth = month;
    const data = assessmentData[month];
    if (!data) {
        document.getElementById('monthContent').innerHTML = '<p>该月份数据正在完善中...</p>';
        return;
    }

    document.querySelectorAll('.month-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`.month-btn:nth-child(${month})`).classList.add('active');

    const content = document.getElementById('monthContent');
    content.innerHTML = `
        <div class="month-content active">
            <div class="month-header">
                <h2>${data.title}</h2>
                <p class="age-info">${data.ageInfo}</p>
            </div>
            ${data.sections.map(section => `
                <div class="assessment-section">
                    <h3 class="section-title">${section.title}</h3>
                    <div class="assessment-items">
                        ${section.items.map((item, index) => `
                            <div class="assessment-item">
                                <div class="item-header">
                                    <div class="item-title">${item.title}</div>
                                    <div class="assessment-checkbox">
                                        <div class="checkbox-option achieved">
                                            <input type="radio" name="item_${month}_${index}" value="achieved"
                                                   onchange="updateAssessment(${month}, ${index}, 'achieved')">
                                            <label>✓</label>
                                        </div>
                                        <div class="checkbox-option partial">
                                            <input type="radio" name="item_${month}_${index}" value="partial"
                                                   onchange="updateAssessment(${month}, ${index}, 'partial')">
                                            <label>△</label>
                                        </div>
                                        <div class="checkbox-option not-achieved">
                                            <input type="radio" name="item_${month}_${index}" value="not-achieved"
                                                   onchange="updateAssessment(${month}, ${index}, 'not-achieved')">
                                            <label>✗</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="item-description">${item.description}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    loadAssessmentResults(month);
    updateProgress();
}

function updateAssessment(month, itemIndex, value) {
    if (!assessmentResults[month]) assessmentResults[month] = {};
    assessmentResults[month][itemIndex] = value;
    localStorage.setItem('assessmentResults', JSON.stringify(assessmentResults));
    updateProgress();
}

function loadAssessmentResults(month) {
    const results = assessmentResults[month];
    if (!results) return;

    Object.keys(results).forEach(itemIndex => {
        const radio = document.querySelector(`input[name="item_${month}_${itemIndex}"][value="${results[itemIndex]}"]`);
        if (radio) radio.checked = true;
    });
}

function updateProgress() {
    const data = assessmentData[currentMonth];
    if (!data) return;

    const totalItems = data.sections.reduce((sum, section) => sum + section.items.length, 0);
    const completedItems = assessmentResults[currentMonth] ? Object.keys(assessmentResults[currentMonth]).length : 0;
    const progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

    document.getElementById('progressFill').style.width = progress + '%';
    document.getElementById('progressText').textContent =
        `已完成 ${completedItems}/${totalItems} 项评估 (${Math.round(progress)}%)`;
}

function generateReport() {
    const reportSummary = document.getElementById('reportSummary');
    const summaryGrid = document.getElementById('summaryGrid');

    let totalAssessed = 0;
    let totalAchieved = 0;
    let totalPartial = 0;
    let totalNotAchieved = 0;

    Object.keys(assessmentResults).forEach(month => {
        const results = assessmentResults[month];
        Object.values(results).forEach(result => {
            totalAssessed++;
            if (result === 'achieved') totalAchieved++;
            else if (result === 'partial') totalPartial++;
            else if (result === 'not-achieved') totalNotAchieved++;
        });
    });

    summaryGrid.innerHTML = `
        <div class="summary-item">
            <div class="summary-number">${totalAssessed}</div>
            <div class="summary-label">总评估项</div>
        </div>
        <div class="summary-item">
            <div class="summary-number">${totalAchieved}</div>
            <div class="summary-label">已达成</div>
        </div>
        <div class="summary-item">
            <div class="summary-number">${totalPartial}</div>
            <div class="summary-label">部分达成</div>
        </div>
        <div class="summary-item">
            <div class="summary-number">${totalNotAchieved}</div>
            <div class="summary-label">未达成</div>
        </div>
    `;

    reportSummary.style.display = 'block';
}

function exportData() {
    const data = {
        assessmentResults: assessmentResults,
        exportDate: new Date().toISOString(),
        version: '1.0'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `baby-assessment-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function clearData() {
    if (confirm('确定要清除所有评估数据吗？此操作不可恢复。')) {
        assessmentResults = {};
        localStorage.removeItem('assessmentResults');
        showMonth(currentMonth);
        document.getElementById('reportSummary').style.display = 'none';
        alert('评估数据已清除');
    }
}

document.addEventListener('DOMContentLoaded', initializeApp);
</script>
</body>
</html>
